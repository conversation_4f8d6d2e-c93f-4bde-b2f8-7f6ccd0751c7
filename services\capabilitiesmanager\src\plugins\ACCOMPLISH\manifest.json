{"id": "plugin-ACCOMPLISH", "verb": "ACCOMPLISH", "description": "Accomplishes a given goal or creates a plan to achieve it", "explanation": "This plugin takes a goal statement and either returns the result of accomplishing the goal or a plan of tasks to achieve it", "inputDefinitions": [{"name": "goal", "required": true, "type": "string", "description": "The goal to be accomplished or planned for"}], "outputDefinitions": [{"name": "plan", "required": false, "type": "plan", "description": "A plan of tasks to achieve the goal, or a direct answer if the goal can be immediately accomplished"}, {"name": "answer", "required": false, "type": "string", "description": "A solution that matches or achieves the goal"}], "language": "python", "entryPoint": {"main": "main.py", "packageSource": {"type": "local", "path": "./", "requirements": "requirements.txt"}}, "repository": {"type": "local"}, "security": {"permissions": [], "sandboxOptions": {}, "trust": {"signature": "GEr7jE7Y6GDjfKXD2i1yMrIWPOko7GJxg9jPCNrxCae2pD1pvVXdi0YrTWK4SKGZis1G6GZcoTtrob26xt17Iuu9f8O8gX/Cz433TRKo78Akl5ggnQn8fqj1uQmIco6uGcspMxHF0PuNHTrZF5jKG+jVT2clG7HPkUXEYhRc61kD7Z6MaKAjFmg75JUkyaW5S6hNY1wFnmrTLX37mu017QE+65rZWELHzeGV9nbmataVMzCjPZmcvn583tCZTs+H9sC8iVr8kKwvCJ2Y3grmSnr6/8biKgQLlpIQp9x9vv7TuyMV7obicGkgkfvt6HQquynHZA0ForXKwwYbth3smg=="}}, "distribution": {"downloads": 0, "rating": 0}, "version": "1.0.0"}