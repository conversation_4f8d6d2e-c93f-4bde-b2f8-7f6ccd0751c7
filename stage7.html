<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>stage7 - Self-Managing Agent Platform | Seakaytee</title>
    <meta name="description" content="stage7 is an open-source general-purpose agent platform that is self-healing, self-enhancing, self-optimizing, and self-managing. Revolutionary AI agents that accomplish user goals autonomously.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #fff;
            background: #000;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: linear-gradient(135deg, #000080, #0b2c79);
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,128,0.3);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #ffcc00;
            text-decoration: none;
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        nav a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        nav a:hover {
            color: #ffcc00;
        }

        .hero {
            background: linear-gradient(135deg, #111827 0%, #1f2937 25%, #1c6fc7 75%, #000080 100%);
            color: white;
            padding: 8rem 0;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="circuit" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(28,111,199,0.2)"/><path d="M5,10 L15,10 M10,5 L10,15" stroke="rgba(28,111,199,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23circuit)" /></svg>');
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 4rem;
            margin-bottom: 1rem;
            font-weight: 700;
            background: linear-gradient(45deg, #1c6fc7, #5844a3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #ffcc00;
            font-weight: 600;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.7;
        }

        .cta-group {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .cta-button {
            background: linear-gradient(45deg, #1c6fc7, #000080);
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(28,111,199,0.4);
        }

        .cta-button.secondary {
            background: transparent;
            border: 2px solid #1c6fc7;
            color: #1c6fc7;
        }

        .cta-button.secondary:hover {
            background: #1c6fc7;
            color: white;
        }

        .hero-visual {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 400px;
        }

        .ai-animation {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: linear-gradient(45deg, #1c6fc7, #5844a3, #0b2c79);
            animation: pulse 2s ease-in-out infinite;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 50px rgba(28,111,199,0.5);
        }

        .ai-animation::before {
            content: '🤖';
            font-size: 4rem;
            animation: rotate 4s linear infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.1); opacity: 1; }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .features {
            padding: 6rem 0;
            background: linear-gradient(135deg, #1f2937, #111827);
        }

        .features h2 {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 3rem;
            background: linear-gradient(45deg, #1c6fc7, #5844a3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }

        .feature-card {
            background: rgba(28,111,199,0.1);
            padding: 2rem;
            border-radius: 20px;
            border: 1px solid rgba(28,111,199,0.2);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(28,111,199,0.2);
            border-color: rgba(28,111,199,0.4);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #1c6fc7;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #ffcc00;
        }

        .capabilities {
            padding: 6rem 0;
            background: #000;
        }

        .capabilities h2 {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 3rem;
            background: linear-gradient(45deg, #1c6fc7, #5844a3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .capability-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .capability-item {
            background: linear-gradient(135deg, rgba(28,111,199,0.1), rgba(28,111,199,0.05));
            padding: 1.5rem;
            border-radius: 15px;
            border-left: 4px solid #1c6fc7;
            transition: all 0.3s ease;
        }

        .capability-item:hover {
            transform: translateX(10px);
            background: linear-gradient(135deg, rgba(28,111,199,0.2), rgba(28,111,199,0.1));
        }

        .open-source {
            padding: 6rem 0;
            background: linear-gradient(135deg, #1f2937, #000080);
            text-align: center;
        }

        .open-source h2 {
            font-size: 3rem;
            margin-bottom: 2rem;
            color: #ffcc00;
        }

        .open-source p {
            font-size: 1.3rem;
            margin-bottom: 3rem;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .github-button {
            background: #24292e;
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .github-button:hover {
            background: #444;
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(36,41,46,0.4);
        }

        footer {
            background: #111827;
            padding: 3rem 0;
            text-align: center;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            color: #1c6fc7;
            margin-bottom: 1rem;
        }

        .footer-section a {
            color: #ccc;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
            transition: color 0.3s ease;
        }

        .footer-section a:hover {
            color: #1c6fc7;
        }

        .footer-bottom {
            border-top: 1px solid #374151;
            padding-top: 2rem;
            color: #9ca3af;
        }

        @media (max-width: 768px) {
            .hero-content {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.2rem;
            }
            
            .hero p {
                font-size: 1.1rem;
            }
            
            .cta-group {
                justify-content: center;
            }
            
            .features h2, .capabilities h2, .open-source h2 {
                font-size: 2rem;
            }
            
            nav ul {
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="#" class="logo">stage7</a>
                <nav>
                    <ul>
                        <li><a href="#features">Features</a></li>
                        <li><a href="#capabilities">Capabilities</a></li>
                        <li><a href="#open-source">Open Source</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <div class="hero-subtitle">Revolutionary AI Technology</div>
                    <h1>stage7</h1>
                    <p>The world's first truly autonomous AI agent platform that self-heals, self-enhances, self-optimizes, and self-manages. Experience the future of artificial intelligence today.</p>
                    <div class="cta-group">
                        <a href="#" class="cta-button">Get Started</a>
                        <a href="#" class="cta-button secondary">View Documentation</a>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="ai-animation"></div>
                </div>
            </div>
        </div>
    </section>

    <section id="features" class="features">
        <div class="container">
            <h2>Core Features</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🔧</div>
                    <h3>Self-Healing</h3>
                    <p>Automatically detects and fixes issues in real-time, ensuring continuous operation without human intervention.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📈</div>
                    <h3>Self-Enhancing</h3>
                    <p>Continuously learns and improves its capabilities through advanced machine learning algorithms and experience.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>Self-Optimizing</h3>
                    <p>Dynamically adjusts performance parameters to achieve optimal efficiency and resource utilization.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎛️</div>
                    <h3>Self-Managing</h3>
                    <p>Handles complex workflows, resource allocation, and task prioritization autonomously.</p>
                </div>
            </div>
        </div>
    </section>

    <section id="capabilities" class="capabilities">
        <div class="container">
            <h2>What stage7 Can Do</h2>
            <div class="capability-list">
                <div class="capability-item">
                    <h4>🤖 Autonomous Task Execution</h4>
                    <p>Complete complex multi-step tasks without supervision</p>
                </div>
                <div class="capability-item">
                    <h4>🔍 Intelligent Problem Solving</h4>
                    <p>Analyze challenges and develop creative solutions</p>
                </div>
                <div class="capability-item">
                    <h4>📊 Data Analysis & Insights</h4>
                    <p>Process vast amounts of data to extract actionable insights</p>
                </div>
                <div class="capability-item">
                    <h4>🌐 Multi-Platform Integration</h4>
                    <p>Seamlessly connect with various APIs and services</p>
                </div>
                <div class="capability-item">
                    <h4>🎯 Goal-Oriented Planning</h4>
                    <p>Create and execute strategic plans to achieve objectives</p>
                </div>
                <div class="capability-item">
                    <h4>🔄 Continuous Learning</h4>
                    <p>Adapt and improve based on new experiences</p>
                </div>
            </div>
        </div>
    </section>

    <section id="open-source" class="open-source">
        <div class="container">
            <h2>Open Source & Community Driven</h2>
            <p>stage7 is built by developers, for developers. Join our growing community of AI enthusiasts and contributors who are shaping the future of autonomous AI systems.</p>
            <a href="https://github.com/cpravetz/stage7" class="github-button">
                <span>⭐</span>
                View on GitHub
            </a>
        </div>
    </section>

    <footer id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Product</h3>
                    <a href="#">Features</a>
                    <a href="#">Documentation</a>
                    <a href="#">API Reference</a>
                    <a href="#">Tutorials</a>
                </div>
                <div class="footer-section">
                    <h3>Community</h3>
                    <a href="#">GitHub</a>
                    <a href="#">Discord</a>
                    <a href="#">Twitter</a>
                    <a href="#">Blog</a>
                </div>
                <div class="footer-section">
                    <h3>Company</h3>
                    <a href="#">About Seakaytee</a>
                    <a href="#">Careers</a>
                    <a href="#">Privacy Policy</a>
                    <a href="#">Terms of Service</a>
                </div>
                <div class="footer-section">
                    <h3>Support</h3>
                    <a href="#">Help Center</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Status Page</a>
                    <a href="#">Bug Reports</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Seakaytee. All rights reserved. stage7 - Revolutionary AI Agent Platform</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add subtle parallax effect to hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.hero');
            const speed = scrolled * 0.5;
            parallax.style.transform = `translateY(${speed}px)`;
        });

        // Animate feature cards on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Apply animation to feature cards
        document.querySelectorAll('.feature-card, .capability-item').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // Add dynamic color changes to the AI animation using brand colors
        const aiAnimation = document.querySelector('.ai-animation');
        const brandColors = ['#1c6fc7', '#5844a3', '#0b2c79'];
        let colorIndex = 0;
        setInterval(() => {
            const color1 = brandColors[colorIndex % 3];
            const color2 = brandColors[(colorIndex + 1) % 3];
            const color3 = brandColors[(colorIndex + 2) % 3];
            aiAnimation.style.background = `linear-gradient(45deg, ${color1}, ${color2}, ${color3})`;
            colorIndex++;
        }, 2000);
    </script>
</body>
</html>